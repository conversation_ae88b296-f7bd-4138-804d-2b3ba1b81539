using Unity.Burst;
using Unity.Entities;
using Unity.Mathematics;
using Unity.Transforms;
using Avalon.Simulation;
using Avalon.Simulation.MovementV2;

namespace Avalon.Visualization.MovementV2
{
    /// <summary>
    /// Visualization system that interpolates MovementV2 simulation data for smooth rendering.
    /// Follows the same pattern as the existing InterpolationSystem.
    /// </summary>
    [BurstCompile]
    [UpdateInGroup(typeof(PresentationSystemGroup))]
    public partial struct InterpolationV2System : ISystem
    {
        private EntityQuery fixedTimestepQuery;
        private EntityQuery interpolationQuery;
        
        [BurstCompile]
        public void OnCreate(ref SystemState state)
        {
            fixedTimestepQuery = state.GetEntityQuery(typeof(FixedTimestep));
            
            interpolationQuery = state.GetEntityQuery(new EntityQueryDesc
            {
                All = new ComponentType[]
                {
                    typeof(SimulationTransformV2),
                    typeof(PreviousSimulationTransformV2),
                    typeof(LocalTransform)
                }
            });
            
            state.RequireForUpdate<FixedTimestep>();
            state.RequireForUpdate<SimulationTransformV2>();
            state.RequireForUpdate<PreviousSimulationTransformV2>();
            state.RequireForUpdate<LocalTransform>();
        }
        
        [BurstCompile]
        public void OnUpdate(ref SystemState state)
        {
            // Get fixed timestep singleton
            if (fixedTimestepQuery.IsEmpty) return;
            var fixedTimestep = fixedTimestepQuery.GetSingleton<FixedTimestep>();
            
            // Interpolate positions for smooth rendering
            var interpolationJob = new InterpolationV2Job
            {
                alpha = fixedTimestep.interpolationAlpha
            };
            
            state.Dependency = interpolationJob.ScheduleParallel(interpolationQuery, state.Dependency);
        }
    }
    
    /// <summary>
    /// Job that performs interpolation between simulation frames for smooth visualization.
    /// </summary>
    [BurstCompile]
    public partial struct InterpolationV2Job : IJobEntity
    {
        public float alpha;
        
        public void Execute(
            ref LocalTransform localTransform,
            in SimulationTransformV2 simulationTransform,
            in PreviousSimulationTransformV2 previousTransform)
        {
            // Clamp alpha to valid range
            var clampedAlpha = math.clamp(alpha, 0f, 1f);
            
            // Convert deterministic types to float for rendering
            var currentPos = new float3(
                (float)simulationTransform.position.x,
                (float)simulationTransform.position.y,
                (float)simulationTransform.position.z
            );
            
            var previousPos = new float3(
                (float)previousTransform.position.x,
                (float)previousTransform.position.y,
                (float)previousTransform.position.z
            );
            
            var currentRot = new quaternion(
                (float)simulationTransform.rotation.value.x,
                (float)simulationTransform.rotation.value.y,
                (float)simulationTransform.rotation.value.z,
                (float)simulationTransform.rotation.value.w
            );
            
            var previousRot = new quaternion(
                (float)previousTransform.rotation.value.x,
                (float)previousTransform.rotation.value.y,
                (float)previousTransform.rotation.value.z,
                (float)previousTransform.rotation.value.w
            );
            
            var currentScale = (float)simulationTransform.scale;
            var previousScale = (float)previousTransform.scale;
            
            // Check for valid positions (prevent NaN/Infinity interpolation)
            if (math.any(math.isnan(currentPos)) || math.any(math.isnan(previousPos)))
            {
                // Fallback to current position if previous is invalid
                localTransform.Position = currentPos;
            }
            else
            {
                // Linear interpolation for position
                localTransform.Position = math.lerp(previousPos, currentPos, clampedAlpha);
            }
            
            // Safe scale interpolation
            if (math.isnan(currentScale) || math.isnan(previousScale))
            {
                localTransform.Scale = currentScale;
            }
            else
            {
                localTransform.Scale = math.lerp(previousScale, currentScale, clampedAlpha);
            }
            
            // Safe spherical interpolation for rotation (smoother for rotations)
            if (math.any(math.isnan(currentRot.value)) || math.any(math.isnan(previousRot.value)))
            {
                localTransform.Rotation = currentRot;
            }
            else
            {
                localTransform.Rotation = math.slerp(previousRot, currentRot, clampedAlpha);
            }
        }
    }
}
