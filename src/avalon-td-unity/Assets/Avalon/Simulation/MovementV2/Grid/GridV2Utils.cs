using Unity.Burst;
using Unity.Collections;
using Unity.Deterministic.Mathematics;
using Unity.Mathematics;

namespace Avalon.Simulation.MovementV2.Grid
{
    /// <summary>
    /// Utility functions for GridV2 operations.
    /// All functions are burst-compiled for performance.
    /// </summary>
    public static class GridV2Utils
    {
        /// <summary>
        /// Converts world position to grid coordinates.
        /// </summary>
        [BurstCompile]
        public static int2 WorldToGrid(dfloat2 worldPos, dfloat2 worldOrigin, dfloat2 cellSize)
        {
            var relativePos = worldPos - worldOrigin;
            return new int2(
                (int)(relativePos.x / cellSize.x),
                (int)(relativePos.y / cellSize.y)
            );
        }
        
        /// <summary>
        /// Converts grid coordinates to world position (center of cell).
        /// </summary>
        [BurstCompile]
        public static dfloat2 GridToWorld(int2 gridPos, dfloat2 worldOrigin, dfloat2 cellSize)
        {
            return worldOrigin + new dfloat2(
                (dfloat)gridPos.x * cellSize.x + cellSize.x * new dfloat(0.5d),
                (dfloat)gridPos.y * cellSize.y + cellSize.y * new dfloat(0.5d)
            );
        }
        
        /// <summary>
        /// Checks if grid coordinates are valid within grid bounds.
        /// </summary>
        [BurstCompile]
        public static bool IsValidGridPosition(int2 gridPos, int2 gridSize)
        {
            return gridPos.x >= 0 && gridPos.x < gridSize.x && 
                   gridPos.y >= 0 && gridPos.y < gridSize.y;
        }
        
        /// <summary>
        /// Converts 2D grid coordinates to flat array index.
        /// </summary>
        [BurstCompile]
        public static int GridToIndex(int2 gridPos, int2 gridSize)
        {
            return gridPos.y * gridSize.x + gridPos.x;
        }
        
        /// <summary>
        /// Converts flat array index to 2D grid coordinates.
        /// </summary>
        [BurstCompile]
        public static int2 IndexToGrid(int index, int2 gridSize)
        {
            return new int2(index % gridSize.x, index / gridSize.x);
        }
        
        /// <summary>
        /// Gets the offset for a given grid direction.
        /// </summary>
        [BurstCompile]
        public static int2 GetDirectionOffset(GridDirection direction)
        {
            return direction switch
            {
                GridDirection.North => new int2(0, 1),
                GridDirection.Northeast => new int2(1, 1),
                GridDirection.East => new int2(1, 0),
                GridDirection.Southeast => new int2(1, -1),
                GridDirection.South => new int2(0, -1),
                GridDirection.Southwest => new int2(-1, -1),
                GridDirection.West => new int2(-1, 0),
                GridDirection.Northwest => new int2(-1, 1),
                _ => new int2(0, 0)
            };
        }
        
        /// <summary>
        /// Calculates Manhattan distance between two grid positions.
        /// </summary>
        [BurstCompile]
        public static int ManhattanDistance(int2 from, int2 to)
        {
            return math.abs(to.x - from.x) + math.abs(to.y - from.y);
        }
        
        /// <summary>
        /// Calculates Euclidean distance between two grid positions.
        /// </summary>
        [BurstCompile]
        public static dfloat EuclideanDistance(int2 from, int2 to)
        {
            var diff = to - from;
            return dmath.sqrt((dfloat)(diff.x * diff.x + diff.y * diff.y));
        }
        
        /// <summary>
        /// Gets all 8 neighboring grid positions.
        /// </summary>
        [BurstCompile]
        public static void GetNeighbors(int2 gridPos, int2 gridSize, ref NativeList<int2> neighbors)
        {
            neighbors.Clear();
            
            for (int dir = 0; dir < 8; dir++)
            {
                var offset = GetDirectionOffset((GridDirection)dir);
                var neighbor = gridPos + offset;
                
                if (IsValidGridPosition(neighbor, gridSize))
                {
                    neighbors.Add(neighbor);
                }
            }
        }
    }
}
