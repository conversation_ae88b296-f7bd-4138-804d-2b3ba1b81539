using Unity.Collections;
using Unity.Deterministic.Mathematics;
using Unity.Entities;
using Unity.Mathematics;

namespace Avalon.Simulation.MovementV2.Grid
{
    /// <summary>
    /// Core grid component for MovementV2 system.
    /// Represents a 2D grid with deterministic cell data and link traversal.
    /// </summary>
    [System.Serializable]
    public struct GridV2 : IComponentData
    {
        public int2 gridSize;
        public dfloat2 cellSize;
        public dfloat2 worldOrigin;
        public int totalCells;
        
        // Grid update tracking
        public bool needsUpdate;
        public dfloat lastUpdateTime;
        public dfloat updateInterval;
        
        // Grid bounds for validation
        public dfloat2 worldMin;
        public dfloat2 worldMax;
    }
    
    /// <summary>
    /// Individual grid cell data with deterministic properties.
    /// </summary>
    public struct GridCell
    {
        // Basic cell properties
        public dfloat movementCost;
        public byte walkabilityMask; // Bitmask for different movement types
        public bool isBlocked;
        
        // Link traversal data
        public byte linkMask; // Which directions have valid links
        public dfloat2 flowDirection; // For flow field pathfinding
        public dfloat distanceToTarget; // Distance field value
        
        // Cell state
        public int occupantCount; // Number of units in this cell
        public dfloat lastOccupiedTime;
        
        // Methods for walkability checking
        public bool IsWalkableFor(MovementV2Type movementType)
        {
            if (isBlocked) return false;
            return (walkabilityMask & (1 << (int)movementType)) != 0;
        }
        
        public void SetWalkable(MovementV2Type movementType, bool walkable)
        {
            if (walkable)
                walkabilityMask |= (byte)(1 << (int)movementType);
            else
                walkabilityMask &= (byte)~(1 << (int)movementType);
        }
        
        // Methods for link traversal
        public bool HasLinkInDirection(GridDirection direction)
        {
            return (linkMask & (1 << (int)direction)) != 0;
        }
        
        public void SetLink(GridDirection direction, bool hasLink)
        {
            if (hasLink)
                linkMask |= (byte)(1 << (int)direction);
            else
                linkMask &= (byte)~(1 << (int)direction);
        }
    }
    
    /// <summary>
    /// Buffer element for storing grid cells.
    /// </summary>
    public struct GridCellBuffer : IBufferElementData
    {
        public GridCell cell;
    }
    
    /// <summary>
    /// Directions for grid link traversal.
    /// </summary>
    public enum GridDirection : byte
    {
        North = 0,
        Northeast = 1,
        East = 2,
        Southeast = 3,
        South = 4,
        Southwest = 5,
        West = 6,
        Northwest = 7
    }
}
