namespace Avalon.Simulation.MovementV2
{
    /// <summary>
    /// Defines different movement types for MovementV2 system.
    /// Each type has different pathfinding and traversal capabilities.
    /// </summary>
    public enum MovementV2Type : byte
    {
        Ground = 0,     // Standard ground movement
        Flying = 1,     // Can move over obstacles and unwalkable terrain
        Amphibious = 2, // Can move on both land and water
        Heavy = 3,      // Slower movement, limited path options
        Teleporting = 4, // Can teleport short distances
        Invisible = 5,   // Can use stealth paths for flanking
        Support = 6      // Special movement for support units
    }
}
