using Unity.Deterministic.Mathematics;
using Unity.Entities;

namespace Avalon.Simulation.MovementV2
{
    /// <summary>
    /// Component for entities that can be targeted by siege units.
    /// Defines available siege slots around the target.
    /// </summary>
    [System.Serializable]
    public struct SiegeTarget : IComponentData
    {
        public int maxSiegeSlots;
        public dfloat siegeRadius;
        public dfloat2 centerPosition;
        public bool allowTrampling; // Whether higher rank units can trample lower rank
    }
    
    /// <summary>
    /// Buffer element representing individual siege slots around a target.
    /// </summary>
    public struct SiegeSlotElement : IBufferElementData
    {
        public dfloat2 position;
        public Entity occupiedBy; // Entity.Null if unoccupied
        public int occupantRank;  // Rank of current occupant
        public bool isReserved;   // Reserved for incoming unit
        public dfloat reservationTime;
        public int slotIndex;
    }
    
    /// <summary>
    /// Component for units that want to siege a target.
    /// </summary>
    [System.Serializable]
    public struct SiegeAgent : IComponentData
    {
        public Entity targetEntity;
        public int assignedSlotIndex;
        public dfloat2 assignedSlotPosition;
        public bool hasAssignedSlot;
        public bool isQueuing; // True if all slots occupied and unit is queuing
        public dfloat2 queuePosition;
    }
}
