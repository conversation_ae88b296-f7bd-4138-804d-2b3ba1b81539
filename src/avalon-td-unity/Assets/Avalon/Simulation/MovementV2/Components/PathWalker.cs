using Unity.Collections;
using Unity.Deterministic.Mathematics;
using Unity.Entities;

namespace Avalon.Simulation.MovementV2
{
    /// <summary>
    /// Component for units that follow calculated paths.
    /// Handles path traversal and local avoidance.
    /// </summary>
    [System.Serializable]
    public struct PathWalker : IComponentData
    {
        // Current path state
        public int currentWaypointIndex;
        public dfloat2 currentTarget;
        public dfloat2 nextWaypoint;
        
        // Path following parameters
        public dfloat waypointReachThreshold;
        public dfloat pathFollowingStrength;
        
        // Local avoidance state
        public dfloat2 avoidanceForce;
        public dfloat lastAvoidanceTime;
        
        // Path validation
        public bool pathValid;
        public dfloat pathCalculationTime;
        public int pathVersion; // Incremented when path changes
    }
    
    /// <summary>
    /// Buffer element for storing path waypoints.
    /// Each waypoint represents a grid position to traverse.
    /// </summary>
    public struct PathWaypoint : IBufferElementData
    {
        public dfloat2 position;
        public int gridX;
        public int gridY;
        public dfloat cost; // Movement cost for this waypoint
    }
}
