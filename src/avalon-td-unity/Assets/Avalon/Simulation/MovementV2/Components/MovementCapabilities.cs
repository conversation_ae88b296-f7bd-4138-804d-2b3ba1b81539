using Unity.Deterministic.Mathematics;
using Unity.Entities;

namespace Avalon.Simulation.MovementV2
{
    /// <summary>
    /// Component defining special movement capabilities for different AI types.
    /// Allows for extensible movement behaviors like teleporting, invisibility, etc.
    /// </summary>
    [System.Serializable]
    public struct MovementCapabilities : IComponentData
    {
        // Basic capabilities flags
        public bool canTeleport;
        public bool canFly;
        public bool canSwim;
        public bool canClimb;
        public bool canPhase; // Move through walls
        
        // Stealth capabilities
        public bool canGoInvisible;
        public dfloat invisibilityDuration;
        public dfloat invisibilityCooldown;
        public dfloat lastInvisibilityTime;
        
        // Teleportation capabilities
        public dfloat teleportRange;
        public dfloat teleportCooldown;
        public dfloat lastTeleportTime;
        public int teleportCharges;
        public int maxTeleportCharges;
        
        // Support capabilities
        public bool canBoostAllies;
        public dfloat boostRadius;
        public dfloat speedBoostMultiplier;
        
        // Special movement costs
        public dfloat flyingSpeedMultiplier;
        public dfloat swimmingSpeedMultiplier;
        public dfloat climbingSpeedMultiplier;
    }
    
    /// <summary>
    /// Component for units currently using special movement abilities.
    /// </summary>
    [System.Serializable]
    public struct ActiveMovementAbility : IComponentData
    {
        public MovementAbilityType abilityType;
        public dfloat startTime;
        public dfloat duration;
        public dfloat2 targetPosition; // For teleport
        public bool isChanneling;
    }
    
    /// <summary>
    /// Types of special movement abilities.
    /// </summary>
    public enum MovementAbilityType : byte
    {
        None = 0,
        Teleport = 1,
        Invisibility = 2,
        Flight = 3,
        Phase = 4,
        SpeedBoost = 5
    }
}
