using Unity.Deterministic.Mathematics;
using Unity.Entities;

namespace Avalon.Simulation.MovementV2
{
    /// <summary>
    /// Deterministic simulation transform for MovementV2 system.
    /// Separates simulation state from visualization rendering.
    /// </summary>
    [System.Serializable]
    public struct SimulationTransformV2 : IComponentData
    {
        public dfloat3 position;
        public dquaternion rotation;
        public dfloat scale;
        
        // Movement-specific data
        public dfloat2 velocity;
        public dfloat speed;
        public dfloat3 facingDirection;
    }
    
    /// <summary>
    /// Previous frame's simulation transform for interpolation.
    /// </summary>
    [System.Serializable]
    public struct PreviousSimulationTransformV2 : IComponentData
    {
        public dfloat3 position;
        public dquaternion rotation;
        public dfloat scale;
        public dfloat2 velocity;
        public dfloat speed;
    }
}
