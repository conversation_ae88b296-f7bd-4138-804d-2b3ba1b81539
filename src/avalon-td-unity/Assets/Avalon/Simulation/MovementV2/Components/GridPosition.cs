using Unity.Deterministic.Mathematics;
using Unity.Entities;
using Unity.Mathematics;

namespace Avalon.Simulation.MovementV2
{
    /// <summary>
    /// Component that tracks an entity's position on the MovementV2 grid.
    /// Used for efficient spatial queries and pathfinding.
    /// </summary>
    [System.Serializable]
    public struct GridPosition : IComponentData
    {
        public int2 gridCoordinates;
        public dfloat2 worldPosition;
        public dfloat2 previousWorldPosition;
        
        // Grid cell information
        public int cellIndex; // Flattened grid index for quick lookups
        public bool isValidPosition;
        
        // Movement tracking
        public bool hasMovedThisFrame;
        public dfloat movementDistance;
    }
    
    /// <summary>
    /// Component for entities that need to track their movement history
    /// for interpolation and debugging purposes.
    /// </summary>
    [System.Serializable]
    public struct MovementHistory : IComponentData
    {
        public dfloat2 previousPosition;
        public dfloat2 previousVelocity;
        public dfloat previousSpeed;
        public dfloat deltaTime;
    }
}
