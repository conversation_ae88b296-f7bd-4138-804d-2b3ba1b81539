using Unity.Deterministic.Mathematics;
using Unity.Entities;

namespace Avalon.Simulation.MovementV2
{
    /// <summary>
    /// Core component for MovementV2 system agents.
    /// Contains deterministic movement properties and state.
    /// </summary>
    [System.Serializable]
    public struct MovementV2Agent : IComponentData
    {
        // Movement properties (deterministic)
        public dfloat maxSpeed;
        public dfloat acceleration;
        public dfloat deceleration;
        public dfloat rotationSpeed;
        
        // Current movement state
        public dfloat2 velocity;
        public dfloat currentSpeed;
        
        // Movement capabilities
        public MovementV2Type movementType;
        public int unitRank; // For siege slot trampling (higher rank can trample lower)
        
        // Pathfinding state
        public bool hasPath;
        public bool isMoving;
        public bool reachedDestination;
        
        // Local avoidance settings
        public bool useLocalAvoidance;
        public dfloat avoidanceRadius;
        public dfloat avoidanceStrength;
        
        // Target proximity settings
        public dfloat targetProximityThreshold;
        public dfloat stoppingDistance;
    }
}
