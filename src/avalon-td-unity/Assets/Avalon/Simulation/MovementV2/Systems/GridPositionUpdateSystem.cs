using Unity.Burst;
using Unity.Collections;
using Unity.Entities;
using Avalon.Simulation;
using Avalon.Simulation.MovementV2.Grid;
using Unity.Deterministic.Mathematics;
using Unity.Jobs;

namespace Avalon.Simulation.MovementV2.Systems
{
    /// <summary>
    /// System that updates grid positions and maintains grid occupancy data.
    /// Runs after movement to ensure grid state is consistent.
    /// </summary>
    [BurstCompile]
    [UpdateInGroup(typeof(MovementV2SystemGroup))]
    [UpdateAfter(typeof(PathWalkingSystem))]
    public partial struct GridPositionUpdateSystem : ISystem
    {
        private EntityQuery gridPositionQuery;
        private EntityQuery gridQuery;
        private EntityQuery fixedTimestepQuery;
        
        [BurstCompile]
        public void OnCreate(ref SystemState state)
        {
            gridPositionQuery = state.GetEntityQuery(new EntityQueryDesc
            {
                All = new ComponentType[]
                {
                    typeof(GridPosition),
                    typeof(MovementV2Agent)
                }
            });
            
            gridQuery = state.GetEntityQuery(new EntityQueryDesc
            {
                All = new ComponentType[]
                {
                    typeof(GridV2),
                    typeof(GridCellBuffer)
                }
            });
            
            fixedTimestepQuery = state.GetEntityQuery(typeof(FixedTimestep));
            
            state.RequireForUpdate<FixedTimestep>();
            state.RequireForUpdate<GridV2>();
        }
        
        [BurstCompile]
        public void OnUpdate(ref SystemState state)
        {
            // Get fixed timestep singleton
            if (fixedTimestepQuery.IsEmpty) return;
            var fixedTimestep = fixedTimestepQuery.GetSingleton<FixedTimestep>();
            
            // Only process on fixed ticks
            if (!fixedTimestep.shouldProcessTick) return;
            
            // Get grid data
            if (gridQuery.IsEmpty) return;
            var gridEntity = gridQuery.GetSingletonEntity();
            var gridData = SystemAPI.GetComponent<GridV2>(gridEntity);
            var gridCellBuffer = SystemAPI.GetBuffer<GridCellBuffer>(gridEntity);
            
            // Update grid positions
            var updateJob = new GridPositionUpdateJob
            {
                currentTime = fixedTimestep.currentTime,
                gridData = gridData
            };
            
            state.Dependency = updateJob.ScheduleParallel(gridPositionQuery, state.Dependency);
            
            // Update grid occupancy
            var occupancyJob = new GridOccupancyUpdateJob
            {
                currentTime = fixedTimestep.currentTime,
                gridData = gridData,
                gridCells = gridCellBuffer.AsNativeArray(),
                gridPositionLookup = SystemAPI.GetComponentLookup<GridPosition>(true)
            };
            
            var allEntities = gridPositionQuery.ToEntityArray(Allocator.TempJob);
            state.Dependency = occupancyJob.Schedule(allEntities.Length, 32, state.Dependency);
            state.Dependency = allEntities.Dispose(state.Dependency);
        }
    }
    
    /// <summary>
    /// Job that updates grid positions for MovementV2 agents.
    /// </summary>
    [BurstCompile]
    public partial struct GridPositionUpdateJob : IJobEntity
    {
        public dfloat currentTime;
        [ReadOnly] public GridV2 gridData;
        
        public void Execute(ref GridPosition gridPosition, ref MovementHistory movementHistory)
        {
            // Update movement history
            movementHistory.previousPosition = gridPosition.previousWorldPosition;
            movementHistory.deltaTime = currentTime - movementHistory.deltaTime;
            
            // Reset frame flags
            gridPosition.hasMovedThisFrame = false;
            gridPosition.movementDistance = dfloat.Zero;
            
            // Validate grid position
            if (GridV2Utils.IsValidGridPosition(gridPosition.gridCoordinates, gridData.gridSize))
            {
                gridPosition.isValidPosition = true;
                gridPosition.cellIndex = GridV2Utils.GridToIndex(gridPosition.gridCoordinates, gridData.gridSize);
            }
            else
            {
                gridPosition.isValidPosition = false;
            }
        }
    }
    
    /// <summary>
    /// Job that updates grid cell occupancy data.
    /// </summary>
    [BurstCompile]
    public struct GridOccupancyUpdateJob : IJobParallelFor
    {
        public dfloat currentTime;
        [ReadOnly] public GridV2 gridData;
        
        public NativeArray<GridCellBuffer> gridCells;
        [ReadOnly] public ComponentLookup<GridPosition> gridPositionLookup;
        
        public void Execute(int index)
        {
            // This job would update occupancy counts for each grid cell
            // For now, we'll just reset the occupancy data
            // In a full implementation, you'd iterate through all entities
            // and count how many are in each cell
            
            var cellElement = gridCells[index];
            var cell = cellElement.cell;
            
            // Reset occupancy (this is simplified)
            cell.occupantCount = 0;
            cell.lastOccupiedTime = currentTime;
            
            cellElement.cell = cell;
            gridCells[index] = cellElement;
        }
    }
}
