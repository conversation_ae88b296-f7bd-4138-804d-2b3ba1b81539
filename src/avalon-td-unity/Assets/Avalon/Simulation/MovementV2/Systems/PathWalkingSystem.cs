using Unity.Burst;
using Unity.Collections;
using Unity.Entities;
using Avalon.Simulation;
using Avalon.Simulation.MovementV2.Grid;
using Avalon.Simulation.MovementV2.Jobs;

namespace Avalon.Simulation.MovementV2.Systems
{
    /// <summary>
    /// System that processes path walking for MovementV2 agents.
    /// Handles movement along calculated paths with local avoidance.
    /// </summary>
    [BurstCompile]
    [UpdateInGroup(typeof(MovementV2SystemGroup))]
    [UpdateAfter(typeof(PathfindingSystem))]
    [UpdateBefore(typeof(GridPositionUpdateSystem))]
    public partial struct PathWalkingSystem : ISystem
    {
        private EntityQuery pathWalkingQuery;
        private EntityQuery gridQuery;
        private EntityQuery fixedTimestepQuery;
        private EntityQuery allAgentsQuery;
        
        [BurstCompile]
        public void OnCreate(ref SystemState state)
        {
            pathWalkingQuery = state.GetEntityQuery(new EntityQueryDesc
            {
                All = new ComponentType[]
                {
                    typeof(MovementV2Agent),
                    typeof(PathWalker),
                    typeof(GridPosition),
                    typeof(PathWaypoint)
                }
            });
            
            gridQuery = state.GetEntityQuery(new EntityQueryDesc
            {
                All = new ComponentType[]
                {
                    typeof(GridV2),
                    typeof(GridCellBuffer)
                }
            });
            
            allAgentsQuery = state.GetEntityQuery(new EntityQueryDesc
            {
                All = new ComponentType[]
                {
                    typeof(MovementV2Agent),
                    typeof(GridPosition)
                }
            });
            
            fixedTimestepQuery = state.GetEntityQuery(typeof(FixedTimestep));
            
            state.RequireForUpdate<FixedTimestep>();
            state.RequireForUpdate<GridV2>();
        }
        
        [BurstCompile]
        public void OnUpdate(ref SystemState state)
        {
            // Get fixed timestep singleton
            if (fixedTimestepQuery.IsEmpty) return;
            var fixedTimestep = fixedTimestepQuery.GetSingleton<FixedTimestep>();
            
            // Only process on fixed ticks
            if (!fixedTimestep.shouldProcessTick) return;
            
            // Get grid data
            if (gridQuery.IsEmpty) return;
            var gridEntity = gridQuery.GetSingletonEntity();
            var gridData = SystemAPI.GetComponent<GridV2>(gridEntity);
            var gridCells = SystemAPI.GetBuffer<GridCellBuffer>(gridEntity).Reinterpret<GridCell>().AsNativeArray();
            
            // Get all agents for local avoidance
            var allAgents = allAgentsQuery.ToEntityArray(Allocator.TempJob);
            
            // Process path walking
            var pathWalkingJob = new PathWalkingJob
            {
                deltaTime = fixedTimestep.tickDuration,
                gridCells = gridCells,
                gridData = gridData,
                gridPositionLookup = SystemAPI.GetComponentLookup<GridPosition>(true),
                allAgents = allAgents,
                agentLookup = SystemAPI.GetComponentLookup<MovementV2Agent>(true)
            };
            
            state.Dependency = pathWalkingJob.ScheduleParallel(pathWalkingQuery, state.Dependency);
            
            // Dispose the agents array after the job completes
            state.Dependency = allAgents.Dispose(state.Dependency);
        }
    }
}
