using Unity.Burst;
using Unity.Collections;
using Unity.Deterministic.Mathematics;
using Unity.Entities;
using Unity.Mathematics;
using Avalon.Simulation;
using Avalon.Simulation.MovementV2.Grid;

namespace Avalon.Simulation.MovementV2.Systems
{
    /// <summary>
    /// System that manages the MovementV2 grid, including initialization and updates.
    /// </summary>
    [BurstCompile]
    [UpdateInGroup(typeof(MovementV2SystemGroup))]
    public partial struct GridV2System : ISystem
    {
        private EntityQuery gridQuery;
        private EntityQuery fixedTimestepQuery;
        
        [BurstCompile]
        public void OnCreate(ref SystemState state)
        {
            gridQuery = state.GetEntityQuery(new EntityQueryDesc
            {
                All = new ComponentType[]
                {
                    typeof(GridV2),
                    typeof(GridCellBuffer)
                }
            });
            
            fixedTimestepQuery = state.GetEntityQuery(typeof(FixedTimestep));
            
            state.RequireForUpdate<FixedTimestep>();
            
            // Create default grid if none exists
            CreateDefaultGrid(ref state);
        }
        
        [BurstCompile]
        public void OnUpdate(ref SystemState state)
        {
            // Get fixed timestep singleton
            if (fixedTimestepQuery.IsEmpty) return;
            var fixedTimestep = fixedTimestepQuery.GetSingleton<FixedTimestep>();
            
            // Only process on fixed ticks
            if (!fixedTimestep.shouldProcessTick) return;
            
            // Update grids that need updating
            var updateJob = new GridUpdateJob
            {
                currentTime = fixedTimestep.currentTime
            };
            
            state.Dependency = updateJob.ScheduleParallel(gridQuery, state.Dependency);
        }
        
        private void CreateDefaultGrid(ref SystemState state)
        {
            // Check if grid already exists
            if (!gridQuery.IsEmpty) return;
            
            // Create default grid entity
            var gridEntity = state.EntityManager.CreateEntity();
            
            // Set up grid component
            var gridData = new GridV2
            {
                gridSize = new int2(100, 100),
                cellSize = new dfloat2(new dfloat(1.0d), new dfloat(1.0d)),
                worldOrigin = new dfloat2(new dfloat(-50.0d), new dfloat(-50.0d)),
                totalCells = 100 * 100,
                needsUpdate = true,
                lastUpdateTime = dfloat.Zero,
                updateInterval = new dfloat(1.0d),
                worldMin = new dfloat2(new dfloat(-50.0d), new dfloat(-50.0d)),
                worldMax = new dfloat2(new dfloat(50.0d), new dfloat(50.0d))
            };
            
            state.EntityManager.AddComponentData(gridEntity, gridData);
            
            // Initialize grid cells
            var cellBuffer = state.EntityManager.AddBuffer<GridCellBuffer>(gridEntity);
            InitializeGridCells(cellBuffer, gridData);
        }
        
        private void InitializeGridCells(DynamicBuffer<GridCellBuffer> cellBuffer, GridV2 gridData)
        {
            cellBuffer.ResizeUninitialized(gridData.totalCells);
            
            for (int y = 0; y < gridData.gridSize.y; y++)
            {
                for (int x = 0; x < gridData.gridSize.x; x++)
                {
                    int index = y * gridData.gridSize.x + x;
                    
                    var cell = new GridCell
                    {
                        movementCost = dfloat.One,
                        walkabilityMask = 0xFF, // All movement types can walk by default
                        isBlocked = false,
                        linkMask = 0xFF, // All directions linked by default
                        flowDirection = dfloat2.zero,
                        distanceToTarget = dfloat.MAX_VALUE,
                        occupantCount = 0,
                        lastOccupiedTime = dfloat.Zero
                    };
                    
                    // Set walkable for all movement types
                    for (int mt = 0; mt < 7; mt++)
                    {
                        cell.SetWalkable((MovementV2Type)mt, true);
                    }
                    
                    // Set links for all directions (except edges)
                    for (int dir = 0; dir < 8; dir++)
                    {
                        var offset = GridV2Utils.GetDirectionOffset((GridDirection)dir);
                        var neighborPos = new int2(x, y) + offset;
                        
                        if (GridV2Utils.IsValidGridPosition(neighborPos, gridData.gridSize))
                        {
                            cell.SetLink((GridDirection)dir, true);
                        }
                    }
                    
                    cellBuffer[index] = new GridCellBuffer { cell = cell };
                }
            }
        }
    }
    
    /// <summary>
    /// Job that updates grid data when needed.
    /// </summary>
    [BurstCompile]
    public partial struct GridUpdateJob : IJobEntity
    {
        public dfloat currentTime;
        
        public void Execute(ref GridV2 grid, DynamicBuffer<GridCellBuffer> cellBuffer)
        {
            // Check if grid needs updating
            bool shouldUpdate = grid.needsUpdate || 
                               (currentTime - grid.lastUpdateTime) > grid.updateInterval;
            
            if (!shouldUpdate) return;
            
            // Update grid cells (this could include dynamic obstacles, etc.)
            UpdateGridCells(cellBuffer, grid);
            
            // Mark as updated
            grid.needsUpdate = false;
            grid.lastUpdateTime = currentTime;
        }
        
        [BurstCompile]
        private void UpdateGridCells(DynamicBuffer<GridCellBuffer> cellBuffer, GridV2 grid)
        {
            // This is where you would update dynamic obstacles, occupancy, etc.
            // For now, we'll just reset occupancy counts
            
            for (int i = 0; i < cellBuffer.Length; i++)
            {
                var cellElement = cellBuffer[i];
                var cell = cellElement.cell;
                
                // Reset occupancy (will be updated by movement systems)
                cell.occupantCount = 0;
                
                cellElement.cell = cell;
                cellBuffer[i] = cellElement;
            }
        }
    }
}
