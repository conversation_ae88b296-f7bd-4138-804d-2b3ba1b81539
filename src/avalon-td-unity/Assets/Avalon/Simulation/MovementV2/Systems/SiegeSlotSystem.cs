using Unity.Burst;
using Unity.Collections;
using Unity.Deterministic.Mathematics;
using Unity.Entities;
using Unity.Mathematics;
using Avalon.Simulation;

namespace Avalon.Simulation.MovementV2.Systems
{
    /// <summary>
    /// System that manages siege slot assignment and trampling mechanics.
    /// Handles N enemies targeting specific positions with rank-based priority.
    /// </summary>
    [BurstCompile]
    [UpdateInGroup(typeof(MovementV2SystemGroup))]
    [UpdateBefore(typeof(PathfindingSystem))]
    public partial struct SiegeSlotSystem : ISystem
    {
        private EntityQuery siegeTargetQuery;
        private EntityQuery siegeAgentQuery;
        private EntityQuery fixedTimestepQuery;
        
        [BurstCompile]
        public void OnCreate(ref SystemState state)
        {
            // Query for entities with siege targets
            siegeTargetQuery = state.GetEntityQuery(new EntityQueryDesc
            {
                All = new ComponentType[]
                {
                    typeof(SiegeTarget),
                    typeof(SiegeSlotElement)
                }
            });
            
            // Query for entities wanting to siege
            siegeAgentQuery = state.GetEntityQuery(new EntityQueryDesc
            {
                All = new ComponentType[]
                {
                    typeof(SiegeAgent),
                    typeof(MovementV2Agent),
                    typeof(GridPosition)
                }
            });
            
            fixedTimestepQuery = state.GetEntityQuery(typeof(FixedTimestep));
            
            state.RequireForUpdate<FixedTimestep>();
        }
        
        [BurstCompile]
        public void OnUpdate(ref SystemState state)
        {
            // Get fixed timestep singleton
            if (fixedTimestepQuery.IsEmpty) return;
            var fixedTimestep = fixedTimestepQuery.GetSingleton<FixedTimestep>();
            
            // Only process on fixed ticks
            if (!fixedTimestep.shouldProcessTick) return;
            
            // Process siege slot assignments
            var assignmentJob = new SiegeSlotAssignmentJob
            {
                currentTime = fixedTimestep.currentTime,
                siegeAgentLookup = SystemAPI.GetComponentLookup<SiegeAgent>(),
                movementAgentLookup = SystemAPI.GetComponentLookup<MovementV2Agent>(true),
                gridPositionLookup = SystemAPI.GetComponentLookup<GridPosition>(true)
            };
            
            state.Dependency = assignmentJob.ScheduleParallel(siegeTargetQuery, state.Dependency);
            
            // Process siege agents looking for targets
            var seekingJob = new SiegeAgentSeekingJob
            {
                currentTime = fixedTimestep.currentTime,
                siegeTargetLookup = SystemAPI.GetComponentLookup<SiegeTarget>(true),
                siegeSlotLookup = SystemAPI.GetBufferLookup<SiegeSlotElement>(),
                siegeAgentLookup = SystemAPI.GetComponentLookup<SiegeAgent>()
            };
            
            state.Dependency = seekingJob.ScheduleParallel(siegeAgentQuery, state.Dependency);
        }
    }
    
    /// <summary>
    /// Job that processes siege slot assignments for targets.
    /// </summary>
    [BurstCompile]
    public partial struct SiegeSlotAssignmentJob : IJobEntity
    {
        public dfloat currentTime;
        
        public ComponentLookup<SiegeAgent> siegeAgentLookup;
        [ReadOnly] public ComponentLookup<MovementV2Agent> movementAgentLookup;
        [ReadOnly] public ComponentLookup<GridPosition> gridPositionLookup;
        
        public void Execute(ref SiegeTarget siegeTarget, DynamicBuffer<SiegeSlotElement> siegeSlots)
        {
            // Initialize siege slots if needed
            if (siegeSlots.Length != siegeTarget.maxSiegeSlots)
            {
                InitializeSiegeSlots(ref siegeTarget, siegeSlots);
            }
            
            // Update siege slot positions based on target center
            UpdateSiegeSlotPositions(siegeTarget, siegeSlots);
            
            // Process slot occupancy and trampling
            for (int i = 0; i < siegeSlots.Length; i++)
            {
                var slot = siegeSlots[i];
                
                // Check if current occupant is still valid
                if (slot.occupiedBy != Entity.Null)
                {
                    if (!ValidateSlotOccupant(slot, currentTime))
                    {
                        // Clear invalid occupant
                        slot.occupiedBy = Entity.Null;
                        slot.occupantRank = 0;
                        slot.isReserved = false;
                        siegeSlots[i] = slot;
                    }
                }
                
                // Clear expired reservations
                if (slot.isReserved && (currentTime - slot.reservationTime) > new dfloat(5.0d))
                {
                    slot.isReserved = false;
                    slot.reservationTime = dfloat.Zero;
                    siegeSlots[i] = slot;
                }
            }
        }
        
        [BurstCompile]
        private void InitializeSiegeSlots(ref SiegeTarget siegeTarget, DynamicBuffer<SiegeSlotElement> siegeSlots)
        {
            siegeSlots.Clear();
            
            // Create slots in a circle around the target
            dfloat angleStep = new dfloat(2.0d * 3.14159265359d) / (dfloat)siegeTarget.maxSiegeSlots;
            
            for (int i = 0; i < siegeTarget.maxSiegeSlots; i++)
            {
                dfloat angle = (dfloat)i * angleStep;
                dfloat2 offset = new dfloat2(
                    dmath.cos(angle) * siegeTarget.siegeRadius,
                    dmath.sin(angle) * siegeTarget.siegeRadius
                );
                
                var slot = new SiegeSlotElement
                {
                    position = siegeTarget.centerPosition + offset,
                    occupiedBy = Entity.Null,
                    occupantRank = 0,
                    isReserved = false,
                    reservationTime = dfloat.Zero,
                    slotIndex = i
                };
                
                siegeSlots.Add(slot);
            }
        }
        
        [BurstCompile]
        private void UpdateSiegeSlotPositions(SiegeTarget siegeTarget, DynamicBuffer<SiegeSlotElement> siegeSlots)
        {
            dfloat angleStep = new dfloat(2.0d * 3.14159265359d) / (dfloat)siegeTarget.maxSiegeSlots;
            
            for (int i = 0; i < siegeSlots.Length; i++)
            {
                dfloat angle = (dfloat)i * angleStep;
                dfloat2 offset = new dfloat2(
                    dmath.cos(angle) * siegeTarget.siegeRadius,
                    dmath.sin(angle) * siegeTarget.siegeRadius
                );
                
                var slot = siegeSlots[i];
                slot.position = siegeTarget.centerPosition + offset;
                siegeSlots[i] = slot;
            }
        }
        
        [BurstCompile]
        private bool ValidateSlotOccupant(SiegeSlotElement slot, dfloat currentTime)
        {
            if (slot.occupiedBy == Entity.Null) return false;
            
            // Check if the occupant still exists and has the required components
            if (!siegeAgentLookup.HasComponent(slot.occupiedBy) ||
                !gridPositionLookup.HasComponent(slot.occupiedBy))
            {
                return false;
            }
            
            // Check if occupant is still close to the slot
            var occupantPosition = gridPositionLookup[slot.occupiedBy].worldPosition;
            var distanceToSlot = dmath.length(occupantPosition - slot.position);
            
            // Allow some tolerance for movement
            return distanceToSlot <= new dfloat(2.0d);
        }
    }
    
    /// <summary>
    /// Job that processes siege agents looking for available slots.
    /// </summary>
    [BurstCompile]
    public partial struct SiegeAgentSeekingJob : IJobEntity
    {
        public dfloat currentTime;

        [ReadOnly] public ComponentLookup<SiegeTarget> siegeTargetLookup;
        public BufferLookup<SiegeSlotElement> siegeSlotLookup;
        public ComponentLookup<SiegeAgent> siegeAgentLookup;
        
        public void Execute(ref SiegeAgent siegeAgent, in MovementV2Agent movementAgent, in GridPosition gridPosition)
        {
            // Skip if agent already has a slot or target is invalid
            if (siegeAgent.hasAssignedSlot || siegeAgent.targetEntity == Entity.Null)
                return;
            
            if (!siegeTargetLookup.HasComponent(siegeAgent.targetEntity) ||
                !siegeSlotLookup.HasBuffer(siegeAgent.targetEntity))
                return;
            
            var siegeTarget = siegeTargetLookup[siegeAgent.targetEntity];
            var siegeSlots = siegeSlotLookup[siegeAgent.targetEntity];
            
            // Try to find an available slot or trample a lower rank unit
            int bestSlotIndex = FindBestAvailableSlot(siegeSlots, movementAgent.unitRank, siegeTarget.allowTrampling);
            
            if (bestSlotIndex >= 0)
            {
                var slot = siegeSlots[bestSlotIndex];
                
                // Handle trampling if necessary
                if (slot.occupiedBy != Entity.Null && siegeTarget.allowTrampling)
                {
                    // Trample the lower rank unit
                    TrampleOccupant(slot.occupiedBy);
                }
                
                // Assign the slot
                slot.occupiedBy = Entity.Null; // Will be set by the agent
                slot.occupantRank = movementAgent.unitRank;
                slot.isReserved = true;
                slot.reservationTime = currentTime;
                siegeSlots[bestSlotIndex] = slot;
                
                // Update siege agent
                siegeAgent.assignedSlotIndex = bestSlotIndex;
                siegeAgent.assignedSlotPosition = slot.position;
                siegeAgent.hasAssignedSlot = true;
                siegeAgent.isQueuing = false;
            }
            else
            {
                // No available slots, enter queuing mode
                siegeAgent.isQueuing = true;
                siegeAgent.queuePosition = CalculateQueuePosition(siegeTarget, gridPosition.worldPosition);
            }
        }
        
        [BurstCompile]
        private int FindBestAvailableSlot(DynamicBuffer<SiegeSlotElement> siegeSlots, int agentRank, bool allowTrampling)
        {
            int bestSlot = -1;
            
            // First, look for completely empty slots
            for (int i = 0; i < siegeSlots.Length; i++)
            {
                var slot = siegeSlots[i];
                if (slot.occupiedBy == Entity.Null && !slot.isReserved)
                {
                    return i;
                }
            }
            
            // If trampling is allowed, look for slots with lower rank occupants
            if (allowTrampling)
            {
                for (int i = 0; i < siegeSlots.Length; i++)
                {
                    var slot = siegeSlots[i];
                    if (slot.occupiedBy != Entity.Null && slot.occupantRank < agentRank)
                    {
                        return i;
                    }
                }
            }
            
            return bestSlot;
        }
        
        [BurstCompile]
        private void TrampleOccupant(Entity occupant)
        {
            // Force the trampled unit to find a new target or queue
            if (siegeAgentLookup.HasComponent(occupant))
            {
                var trampledAgent = siegeAgentLookup[occupant];
                trampledAgent.hasAssignedSlot = false;
                trampledAgent.assignedSlotIndex = -1;
                trampledAgent.isQueuing = true;
                siegeAgentLookup[occupant] = trampledAgent;
            }
        }
        
        [BurstCompile]
        private dfloat2 CalculateQueuePosition(SiegeTarget siegeTarget, dfloat2 agentPosition)
        {
            // Calculate a position slightly outside the siege radius for queuing
            var directionFromCenter = dmath.normalize(agentPosition - siegeTarget.centerPosition);
            var queueDistance = siegeTarget.siegeRadius + new dfloat(1.5d);
            return siegeTarget.centerPosition + directionFromCenter * queueDistance;
        }
    }
}
