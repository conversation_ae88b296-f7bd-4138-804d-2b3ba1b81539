using Unity.Burst;
using Unity.Deterministic.Mathematics;
using Unity.Entities;
using Unity.Mathematics;
using Avalon.Simulation;

namespace Avalon.Simulation.MovementV2.Systems
{
    /// <summary>
    /// System that updates SimulationTransformV2 based on MovementV2 data.
    /// Maintains separation between simulation and visualization.
    /// </summary>
    [BurstCompile]
    [UpdateInGroup(typeof(MovementV2SystemGroup))]
    [UpdateAfter(typeof(GridPositionUpdateSystem))]
    public partial struct SimulationTransformV2System : ISystem
    {
        private EntityQuery transformQuery;
        private EntityQuery fixedTimestepQuery;
        
        [BurstCompile]
        public void OnCreate(ref SystemState state)
        {
            transformQuery = state.GetEntityQuery(new EntityQueryDesc
            {
                All = new ComponentType[]
                {
                    typeof(SimulationTransformV2),
                    typeof(PreviousSimulationTransformV2),
                    typeof(MovementV2Agent),
                    typeof(GridPosition)
                }
            });
            
            fixedTimestepQuery = state.GetEntityQuery(typeof(FixedTimestep));
            
            state.RequireForUpdate<FixedTimestep>();
        }
        
        [BurstCompile]
        public void OnUpdate(ref SystemState state)
        {
            // Get fixed timestep singleton
            if (fixedTimestepQuery.IsEmpty) return;
            var fixedTimestep = fixedTimestepQuery.GetSingleton<FixedTimestep>();
            
            // Only process on fixed ticks
            if (!fixedTimestep.shouldProcessTick) return;
            
            // Update simulation transforms
            var updateJob = new UpdateSimulationTransformV2Job
            {
                deltaTime = fixedTimestep.tickDuration
            };
            
            state.Dependency = updateJob.ScheduleParallel(transformQuery, state.Dependency);
        }
    }
    
    /// <summary>
    /// Job that updates SimulationTransformV2 components.
    /// </summary>
    [BurstCompile]
    public partial struct UpdateSimulationTransformV2Job : IJobEntity
    {
        public dfloat deltaTime;
        
        public void Execute(
            ref SimulationTransformV2 simulationTransform,
            ref PreviousSimulationTransformV2 previousTransform,
            in MovementV2Agent agent,
            in GridPosition gridPosition)
        {
            // Store previous state for interpolation
            previousTransform.position = simulationTransform.position;
            previousTransform.rotation = simulationTransform.rotation;
            previousTransform.scale = simulationTransform.scale;
            previousTransform.velocity = simulationTransform.velocity;
            previousTransform.speed = simulationTransform.speed;
            
            // Update current simulation transform
            simulationTransform.position = new dfloat3(
                gridPosition.worldPosition.x,
                dfloat.Zero, // Y is typically 0 for 2D movement
                gridPosition.worldPosition.y
            );
            
            simulationTransform.velocity = agent.velocity;
            simulationTransform.speed = agent.currentSpeed;
            
            // Update rotation based on movement direction
            if (agent.isMoving && dmath.lengthsq(agent.velocity) > new dfloat(0.001d))
            {
                var normalizedVelocity = dmath.normalize(agent.velocity);
                simulationTransform.facingDirection = new dfloat3(
                    normalizedVelocity.x,
                    dfloat.Zero,
                    normalizedVelocity.y
                );
                
                // Calculate Y-axis rotation from 2D velocity
                var angle = dmath.atan2(normalizedVelocity.x, normalizedVelocity.y);
                
                if (dmath.isfinite(angle))
                {
                    simulationTransform.rotation = dquaternion.RotateY(angle);
                }
            }
            
            // Scale remains constant unless modified by abilities
            if (simulationTransform.scale == dfloat.Zero)
            {
                simulationTransform.scale = dfloat.One;
            }
        }
    }
}
