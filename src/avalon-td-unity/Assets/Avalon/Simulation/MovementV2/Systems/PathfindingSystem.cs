using Unity.Burst;
using Unity.Collections;
using Unity.Deterministic.Mathematics;
using Unity.Entities;
using Unity.Mathematics;
using Avalon.Simulation;
using Avalon.Simulation.MovementV2.AI;
using Avalon.Simulation.MovementV2.Grid;
using Avalon.Simulation.MovementV2.Pathfinding;

namespace Avalon.Simulation.MovementV2.Systems
{
    /// <summary>
    /// System that handles pathfinding requests for MovementV2 agents.
    /// Uses A* algorithm for individual unit pathfinding.
    /// </summary>
    [BurstCompile]
    [UpdateInGroup(typeof(MovementV2SystemGroup))]
    [UpdateAfter(typeof(MovementAISystem))]
    [UpdateBefore(typeof(PathWalkingSystem))]
    public partial struct PathfindingSystem : ISystem
    {
        private EntityQuery pathfindingQuery;
        private EntityQuery gridQuery;
        private EntityQuery fixedTimestepQuery;
        
        [BurstCompile]
        public void OnCreate(ref SystemState state)
        {
            pathfindingQuery = state.GetEntityQuery(new EntityQueryDesc
            {
                All = new ComponentType[]
                {
                    typeof(MovementV2Agent),
                    typeof(PathWalker),
                    typeof(GridPosition),
                    typeof(PathWaypoint)
                }
            });
            
            gridQuery = state.GetEntityQuery(new EntityQueryDesc
            {
                All = new ComponentType[]
                {
                    typeof(GridV2),
                    typeof(GridCellBuffer)
                }
            });
            
            fixedTimestepQuery = state.GetEntityQuery(typeof(FixedTimestep));
            
            state.RequireForUpdate<FixedTimestep>();
            state.RequireForUpdate<GridV2>();
        }
        
        [BurstCompile]
        public void OnUpdate(ref SystemState state)
        {
            // Get fixed timestep singleton
            if (fixedTimestepQuery.IsEmpty) return;
            var fixedTimestep = fixedTimestepQuery.GetSingleton<FixedTimestep>();
            
            // Only process on fixed ticks
            if (!fixedTimestep.shouldProcessTick) return;
            
            // Get grid data
            if (gridQuery.IsEmpty) return;
            var gridEntity = gridQuery.GetSingletonEntity();
            var gridData = SystemAPI.GetComponent<GridV2>(gridEntity);
            var gridCells = SystemAPI.GetBuffer<GridCellBuffer>(gridEntity).Reinterpret<GridCell>().AsNativeArray();
            
            // Process pathfinding requests
            var pathfindingJob = new PathfindingJob
            {
                currentTime = fixedTimestep.currentTime,
                gridData = gridData,
                gridCells = gridCells,
                siegeAgentLookup = SystemAPI.GetComponentLookup<SiegeAgent>(true)
            };
            
            state.Dependency = pathfindingJob.ScheduleParallel(pathfindingQuery, state.Dependency);
        }
    }
    
    /// <summary>
    /// Job that processes pathfinding for MovementV2 agents.
    /// </summary>
    [BurstCompile]
    public partial struct PathfindingJob : IJobEntity
    {
        public dfloat currentTime;
        
        [ReadOnly] public GridV2 gridData;
        [ReadOnly] public NativeArray<GridCell> gridCells;
        [ReadOnly] public ComponentLookup<SiegeAgent> siegeAgentLookup;
        
        public void Execute(
            ref MovementV2Agent agent,
            ref PathWalker pathWalker,
            in GridPosition gridPosition,
            DynamicBuffer<PathWaypoint> pathBuffer)
        {
            // Skip if agent doesn't need a new path
            if (agent.hasPath && pathWalker.pathValid && !ShouldRecalculatePath(agent, pathWalker))
                return;
            
            // Determine target position
            dfloat2 targetWorldPos;
            if (!GetTargetPosition(agent, out targetWorldPos))
                return;
            
            // Convert positions to grid coordinates
            var startGrid = gridPosition.gridCoordinates;
            var targetGrid = GridV2Utils.WorldToGrid(targetWorldPos, gridData.worldOrigin, gridData.cellSize);
            
            // Validate target position
            if (!GridV2Utils.IsValidGridPosition(targetGrid, gridData.gridSize))
                return;
            
            // Create pathfinder
            var pathfinder = new AStarPathfinder(
                gridData.gridSize,
                gridData.cellSize,
                gridData.worldOrigin,
                Allocator.Temp
            );
            
            // Find path
            var pathResult = new NativeList<int2>(Allocator.Temp);
            bool pathFound = pathfinder.FindPath(
                startGrid,
                targetGrid,
                agent.movementType,
                gridCells,
                ref pathResult
            );
            
            if (pathFound && pathResult.Length > 0)
            {
                // Convert path to waypoints
                UpdatePathBuffer(pathBuffer, pathResult, gridData);
                
                // Update path walker state
                pathWalker.currentWaypointIndex = 0;
                pathWalker.pathValid = true;
                pathWalker.pathCalculationTime = currentTime;
                pathWalker.pathVersion++;
                
                // Update agent state
                agent.hasPath = true;
                agent.reachedDestination = false;
                
                // Set initial target
                if (pathBuffer.Length > 0)
                {
                    pathWalker.currentTarget = pathBuffer[0].position;
                    if (pathBuffer.Length > 1)
                    {
                        pathWalker.nextWaypoint = pathBuffer[1].position;
                    }
                }
            }
            else
            {
                // No path found
                agent.hasPath = false;
                pathWalker.pathValid = false;
                pathBuffer.Clear();
            }
            
            // Cleanup
            pathResult.Dispose();
            pathfinder.Dispose();
        }
        
        [BurstCompile]
        private bool ShouldRecalculatePath(MovementV2Agent agent, PathWalker pathWalker)
        {
            // Recalculate if path is old or agent is stuck
            dfloat pathAge = currentTime - pathWalker.pathCalculationTime;
            
            if (pathAge > new dfloat(5.0d)) // Path is older than 5 seconds
                return true;
            
            if (!agent.isMoving && agent.hasPath) // Agent is stuck
                return true;
            
            return false;
        }
        
        [BurstCompile]
        private bool GetTargetPosition(MovementV2Agent agent, out dfloat2 targetWorldPos)
        {
            targetWorldPos = dfloat2.zero;
            
            // Check if this is a siege agent
            if (siegeAgentLookup.HasComponent(Entity.Null)) // This would need the actual entity
            {
                // For siege agents, target is the assigned slot position
                // This is simplified - in practice you'd pass the entity to this job
                return false;
            }
            
            // For now, use a simple target (this should be configurable)
            targetWorldPos = new dfloat2(new dfloat(10.0d), new dfloat(10.0d));
            return true;
        }
        
        [BurstCompile]
        private void UpdatePathBuffer(
            DynamicBuffer<PathWaypoint> pathBuffer,
            NativeList<int2> pathResult,
            GridV2 gridData)
        {
            pathBuffer.Clear();
            
            for (int i = 0; i < pathResult.Length; i++)
            {
                var gridPos = pathResult[i];
                var worldPos = GridV2Utils.GridToWorld(gridPos, gridData.worldOrigin, gridData.cellSize);
                
                var waypoint = new PathWaypoint
                {
                    position = worldPos,
                    gridX = gridPos.x,
                    gridY = gridPos.y,
                    cost = dfloat.One // This could be calculated from grid cell
                };
                
                pathBuffer.Add(waypoint);
            }
        }
    }
}
