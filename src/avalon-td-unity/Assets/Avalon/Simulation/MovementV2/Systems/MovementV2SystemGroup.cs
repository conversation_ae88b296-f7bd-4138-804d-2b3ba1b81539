using Unity.Entities;
using FlowField;
using Avalon.Simulation.MovementV2.AI;

namespace Avalon.Simulation.MovementV2.Systems
{
    /// <summary>
    /// System group for MovementV2 that manages all movement-related systems.
    /// Integrates with the existing FlowFieldSystemGroup hierarchy.
    /// </summary>
    [UpdateInGroup(typeof(FlowFieldSystemGroup))]
    [UpdateAfter(typeof(FlowFieldManagerSystem))]
    [UpdateBefore(typeof(Avalon.Simulation.Movement.MovementSystemGroup))]
    [WorldSystemFilter(WorldSystemFilterFlags.Default | WorldSystemFilterFlags.EntitySceneOptimizations)]
    public partial class MovementV2SystemGroup : ComponentSystemGroup
    {
        protected override void OnCreate()
        {
            base.OnCreate();

            // Add systems in proper execution order
            
            // 1. Grid management and updates
            var gridSystem = World.GetOrCreateSystem<GridV2System>();
            
            // 2. Siege slot management (must run before pathfinding)
            var siegeSlotSystem = World.GetOrCreateSystem<SiegeSlotSystem>();
            
            // 3. AI movement capabilities (special abilities)
            var movementAISystem = World.GetOrCreateSystem<MovementAISystem>();
            
            // 4. Pathfinding system (calculates paths)
            var pathfindingSystem = World.GetOrCreateSystem<PathfindingSystem>();
            
            // 5. Path walking and movement execution
            var pathWalkingSystem = World.GetOrCreateSystem<PathWalkingSystem>();
            
            // 6. Grid position updates
            var gridPositionSystem = World.GetOrCreateSystem<GridPositionUpdateSystem>();

            // 7. Simulation transform updates
            var simulationTransformSystem = World.GetOrCreateSystem<SimulationTransformV2System>();

            // Add systems to update list in order
            AddSystemToUpdateList(gridSystem);
            AddSystemToUpdateList(siegeSlotSystem);
            AddSystemToUpdateList(movementAISystem);
            AddSystemToUpdateList(pathfindingSystem);
            AddSystemToUpdateList(pathWalkingSystem);
            AddSystemToUpdateList(gridPositionSystem);
            AddSystemToUpdateList(simulationTransformSystem);
        }
    }
}
