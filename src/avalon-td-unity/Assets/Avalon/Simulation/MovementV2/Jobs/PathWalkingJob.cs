using Unity.Burst;
using Unity.Collections;
using Unity.Deterministic.Mathematics;
using Unity.Entities;
using Unity.Mathematics;
using Avalon.Simulation.MovementV2.Grid;

namespace Avalon.Simulation.MovementV2.Jobs
{
    /// <summary>
    /// Job for processing path walking and local avoidance for MovementV2 agents.
    /// Uses deterministic math for consistent cross-platform behavior.
    /// </summary>
    [BurstCompile]
    public partial struct PathWalkingJob : IJobEntity
    {
        public dfloat deltaTime;
        
        [ReadOnly] public NativeArray<GridCell> gridCells;
        [ReadOnly] public GridV2 gridData;
        [ReadOnly] public ComponentLookup<GridPosition> gridPositionLookup;
        
        // For local avoidance
        [ReadOnly] public NativeArray<Entity> allAgents;
        [ReadOnly] public ComponentLookup<MovementV2Agent> agentLookup;
        
        public void Execute(
            ref MovementV2Agent agent,
            ref PathWalker pathWalker,
            ref GridPosition gridPosition,
            DynamicBuffer<PathWaypoint> pathBuffer)
        {
            // Skip if no path or path is invalid
            if (!agent.hasPath || !pathWalker.pathValid || pathBuffer.Length == 0)
            {
                agent.isMoving = false;
                agent.velocity = dfloat2.zero;
                return;
            }
            
            // Check if we've reached the destination
            if (pathWalker.currentWaypointIndex >= pathBuffer.Length)
            {
                agent.reachedDestination = true;
                agent.isMoving = false;
                agent.velocity = dfloat2.zero;
                return;
            }
            
            // Get current target waypoint
            var currentWaypoint = pathBuffer[pathWalker.currentWaypointIndex];
            pathWalker.currentTarget = currentWaypoint.position;
            
            // Calculate direction to target
            var directionToTarget = pathWalker.currentTarget - gridPosition.worldPosition;
            var distanceToTarget = dmath.length(directionToTarget);
            
            // Check if we've reached the current waypoint
            if (distanceToTarget <= pathWalker.waypointReachThreshold)
            {
                pathWalker.currentWaypointIndex++;
                
                // Set next waypoint if available
                if (pathWalker.currentWaypointIndex < pathBuffer.Length)
                {
                    pathWalker.nextWaypoint = pathBuffer[pathWalker.currentWaypointIndex].position;
                }
                
                return; // Process next waypoint on next frame
            }
            
            // Normalize direction
            var normalizedDirection = directionToTarget / distanceToTarget;
            
            // Calculate desired velocity
            var desiredVelocity = normalizedDirection * agent.maxSpeed;
            
            // Apply local avoidance if enabled
            if (agent.useLocalAvoidance)
            {
                var avoidanceForce = CalculateLocalAvoidance(
                    gridPosition.worldPosition,
                    agent.avoidanceRadius,
                    agent.avoidanceStrength);
                
                pathWalker.avoidanceForce = avoidanceForce;
                desiredVelocity += avoidanceForce;
            }
            
            // Apply steering behavior (smooth acceleration/deceleration)
            var velocityChange = desiredVelocity - agent.velocity;
            var maxAcceleration = agent.acceleration * deltaTime;
            
            if (dmath.lengthsq(velocityChange) > maxAcceleration * maxAcceleration)
            {
                velocityChange = dmath.normalize(velocityChange) * maxAcceleration;
            }
            
            agent.velocity += velocityChange;
            
            // Clamp velocity to max speed
            var currentSpeed = dmath.length(agent.velocity);
            if (currentSpeed > agent.maxSpeed)
            {
                agent.velocity = (agent.velocity / currentSpeed) * agent.maxSpeed;
                currentSpeed = agent.maxSpeed;
            }
            
            agent.currentSpeed = currentSpeed;
            agent.isMoving = currentSpeed > new dfloat(0.001d);
            
            // Update position
            var newPosition = gridPosition.worldPosition + agent.velocity * deltaTime;
            
            // Validate new position is walkable
            if (IsPositionWalkable(newPosition, agent.movementType))
            {
                gridPosition.previousWorldPosition = gridPosition.worldPosition;
                gridPosition.worldPosition = newPosition;
                gridPosition.hasMovedThisFrame = true;
                gridPosition.movementDistance = dmath.length(agent.velocity * deltaTime);
                
                // Update grid coordinates
                var newGridCoords = GridV2Utils.WorldToGrid(newPosition, gridData.worldOrigin, gridData.cellSize);
                if (GridV2Utils.IsValidGridPosition(newGridCoords, gridData.gridSize))
                {
                    gridPosition.gridCoordinates = newGridCoords;
                    gridPosition.cellIndex = GridV2Utils.GridToIndex(newGridCoords, gridData.gridSize);
                    gridPosition.isValidPosition = true;
                }
                else
                {
                    gridPosition.isValidPosition = false;
                }
            }
            else
            {
                // Position is not walkable, try to find alternative
                var adjustedPosition = FindNearestWalkablePosition(newPosition, agent.movementType);
                if (adjustedPosition.HasValue)
                {
                    gridPosition.worldPosition = adjustedPosition.Value;
                }
                else
                {
                    // Can't move, stop the agent
                    agent.velocity = dfloat2.zero;
                    agent.isMoving = false;
                }
            }
        }
        
        [BurstCompile]
        private dfloat2 CalculateLocalAvoidance(dfloat2 position, dfloat avoidanceRadius, dfloat avoidanceStrength)
        {
            var avoidanceForce = dfloat2.zero;
            var radiusSquared = avoidanceRadius * avoidanceRadius;
            
            for (int i = 0; i < allAgents.Length; i++)
            {
                var otherEntity = allAgents[i];
                
                if (!gridPositionLookup.HasComponent(otherEntity) || !agentLookup.HasComponent(otherEntity))
                    continue;
                
                var otherPosition = gridPositionLookup[otherEntity].worldPosition;
                var otherAgent = agentLookup[otherEntity];
                
                var offset = position - otherPosition;
                var distanceSquared = dmath.lengthsq(offset);
                
                if (distanceSquared > dfloat.Zero && distanceSquared < radiusSquared)
                {
                    var distance = dmath.sqrt(distanceSquared);
                    var normalizedOffset = offset / distance;
                    var avoidanceWeight = (avoidanceRadius - distance) / avoidanceRadius;
                    
                    avoidanceForce += normalizedOffset * avoidanceWeight * avoidanceStrength;
                }
            }
            
            return avoidanceForce;
        }
        
        [BurstCompile]
        private bool IsPositionWalkable(dfloat2 worldPosition, MovementV2Type movementType)
        {
            var gridCoords = GridV2Utils.WorldToGrid(worldPosition, gridData.worldOrigin, gridData.cellSize);
            
            if (!GridV2Utils.IsValidGridPosition(gridCoords, gridData.gridSize))
                return false;
            
            var cellIndex = GridV2Utils.GridToIndex(gridCoords, gridData.gridSize);
            return gridCells[cellIndex].IsWalkableFor(movementType);
        }
        
        [BurstCompile]
        private dfloat2? FindNearestWalkablePosition(dfloat2 targetPosition, MovementV2Type movementType)
        {
            var gridCoords = GridV2Utils.WorldToGrid(targetPosition, gridData.worldOrigin, gridData.cellSize);
            
            // Try adjacent cells in a small radius
            for (int radius = 1; radius <= 2; radius++)
            {
                for (int dx = -radius; dx <= radius; dx++)
                {
                    for (int dy = -radius; dy <= radius; dy++)
                    {
                        if (dx == 0 && dy == 0) continue;
                        
                        var testCoords = gridCoords + new int2(dx, dy);
                        
                        if (GridV2Utils.IsValidGridPosition(testCoords, gridData.gridSize))
                        {
                            var cellIndex = GridV2Utils.GridToIndex(testCoords, gridData.gridSize);
                            if (gridCells[cellIndex].IsWalkableFor(movementType))
                            {
                                return GridV2Utils.GridToWorld(testCoords, gridData.worldOrigin, gridData.cellSize);
                            }
                        }
                    }
                }
            }
            
            return null;
        }
    }
}
