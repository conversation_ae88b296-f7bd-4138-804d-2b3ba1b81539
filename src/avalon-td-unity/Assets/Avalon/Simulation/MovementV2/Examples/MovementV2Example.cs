using Unity.Deterministic.Mathematics;
using Unity.Entities;
using Unity.Mathematics;
using Unity.Transforms;

namespace Avalon.Simulation.MovementV2.Examples
{
    /// <summary>
    /// Example demonstrating how to create and configure MovementV2 entities.
    /// This shows the basic setup for different types of movement agents.
    /// </summary>
    public partial class MovementV2Example : SystemBase
    {
        protected override void OnUpdate()
        {
            // This system only runs once to create example entities
            Enabled = false;
            
            CreateGroundUnit();
            CreateFlyingUnit();
            CreateTeleportingUnit();
            CreateSiegeTarget();
            CreateSiegeAgent();
        }
        
        /// <summary>
        /// Creates a basic ground movement unit.
        /// </summary>
        private void CreateGroundUnit()
        {
            var entity = EntityManager.CreateEntity();
            
            // Core movement components
            EntityManager.AddComponentData(entity, new MovementV2Agent
            {
                maxSpeed = new dfloat(5.0d),
                acceleration = new dfloat(10.0d),
                deceleration = new dfloat(15.0d),
                rotationSpeed = new dfloat(180.0d), // degrees per second
                movementType = MovementV2Type.Ground,
                unitRank = 1,
                useLocalAvoidance = true,
                avoidanceRadius = new dfloat(2.0d),
                avoidanceStrength = new dfloat(1.0d),
                targetProximityThreshold = new dfloat(0.5d),
                stoppingDistance = new dfloat(0.1d)
            });
            
            EntityManager.AddComponentData(entity, new PathWalker
            {
                waypointReachThreshold = new dfloat(0.5d),
                pathFollowingStrength = new dfloat(1.0d),
                currentWaypointIndex = 0,
                pathValid = false
            });
            
            EntityManager.AddComponentData(entity, new GridPosition
            {
                worldPosition = new dfloat2(dfloat.Zero, dfloat.Zero),
                isValidPosition = true
            });
            
            // Add path buffer
            EntityManager.AddBuffer<PathWaypoint>(entity);
            
            // Add simulation transforms
            EntityManager.AddComponentData(entity, new SimulationTransformV2
            {
                position = new dfloat3(dfloat.Zero, dfloat.Zero, dfloat.Zero),
                rotation = dquaternion.identity,
                scale = dfloat.One
            });
            
            EntityManager.AddComponentData(entity, new PreviousSimulationTransformV2());
            EntityManager.AddComponentData(entity, new MovementHistory());
            
            // Add Unity transform for rendering
            EntityManager.AddComponentData(entity, LocalTransform.Identity);
        }
        
        /// <summary>
        /// Creates a flying unit with special movement capabilities.
        /// </summary>
        private void CreateFlyingUnit()
        {
            var entity = EntityManager.CreateEntity();
            
            // Core movement components
            EntityManager.AddComponentData(entity, new MovementV2Agent
            {
                maxSpeed = new dfloat(8.0d),
                acceleration = new dfloat(12.0d),
                movementType = MovementV2Type.Flying,
                unitRank = 2,
                useLocalAvoidance = false // Flying units don't need ground avoidance
            });
            
            EntityManager.AddComponentData(entity, new PathWalker());
            EntityManager.AddComponentData(entity, new GridPosition());
            EntityManager.AddBuffer<PathWaypoint>(entity);
            
            // Flying capabilities
            EntityManager.AddComponentData(entity, new MovementCapabilities
            {
                canFly = true,
                flyingSpeedMultiplier = new dfloat(1.2d)
            });
            
            EntityManager.AddComponentData(entity, new ActiveMovementAbility
            {
                abilityType = MovementAbilityType.None
            });
            
            // Transforms
            EntityManager.AddComponentData(entity, new SimulationTransformV2());
            EntityManager.AddComponentData(entity, new PreviousSimulationTransformV2());
            EntityManager.AddComponentData(entity, LocalTransform.Identity);
        }
        
        /// <summary>
        /// Creates a unit with teleportation abilities.
        /// </summary>
        private void CreateTeleportingUnit()
        {
            var entity = EntityManager.CreateEntity();
            
            // Core movement components
            EntityManager.AddComponentData(entity, new MovementV2Agent
            {
                maxSpeed = new dfloat(4.0d),
                acceleration = new dfloat(8.0d),
                movementType = MovementV2Type.Teleporting,
                unitRank = 3
            });
            
            EntityManager.AddComponentData(entity, new PathWalker());
            EntityManager.AddComponentData(entity, new GridPosition());
            EntityManager.AddBuffer<PathWaypoint>(entity);
            
            // Teleportation capabilities
            EntityManager.AddComponentData(entity, new MovementCapabilities
            {
                canTeleport = true,
                teleportRange = new dfloat(10.0d),
                teleportCooldown = new dfloat(5.0d),
                teleportCharges = 2,
                maxTeleportCharges = 2,
                canGoInvisible = true,
                invisibilityDuration = new dfloat(3.0d),
                invisibilityCooldown = new dfloat(10.0d)
            });
            
            EntityManager.AddComponentData(entity, new ActiveMovementAbility());
            
            // Transforms
            EntityManager.AddComponentData(entity, new SimulationTransformV2());
            EntityManager.AddComponentData(entity, new PreviousSimulationTransformV2());
            EntityManager.AddComponentData(entity, LocalTransform.Identity);
        }
        
        /// <summary>
        /// Creates a siege target that can be surrounded by enemies.
        /// </summary>
        private void CreateSiegeTarget()
        {
            var entity = EntityManager.CreateEntity();
            
            // Siege target configuration
            EntityManager.AddComponentData(entity, new SiegeTarget
            {
                maxSiegeSlots = 8,
                siegeRadius = new dfloat(3.0d),
                centerPosition = new dfloat2(new dfloat(10.0d), new dfloat(10.0d)),
                allowTrampling = true
            });
            
            // Initialize siege slots buffer
            var siegeSlots = EntityManager.AddBuffer<SiegeSlotElement>(entity);
            
            // Position and transform
            EntityManager.AddComponentData(entity, new SimulationTransformV2
            {
                position = new dfloat3(new dfloat(10.0d), dfloat.Zero, new dfloat(10.0d)),
                rotation = dquaternion.identity,
                scale = dfloat.One
            });
            
            EntityManager.AddComponentData(entity, LocalTransform.FromPosition(new float3(10f, 0f, 10f)));
        }
        
        /// <summary>
        /// Creates a siege agent that will target siege positions.
        /// </summary>
        private void CreateSiegeAgent()
        {
            var entity = EntityManager.CreateEntity();
            
            // Core movement components
            EntityManager.AddComponentData(entity, new MovementV2Agent
            {
                maxSpeed = new dfloat(3.0d),
                acceleration = new dfloat(6.0d),
                movementType = MovementV2Type.Heavy,
                unitRank = 2, // Can trample rank 1 units
                useLocalAvoidance = true,
                avoidanceRadius = new dfloat(1.5d)
            });
            
            EntityManager.AddComponentData(entity, new PathWalker());
            EntityManager.AddComponentData(entity, new GridPosition());
            EntityManager.AddBuffer<PathWaypoint>(entity);
            
            // Siege behavior
            EntityManager.AddComponentData(entity, new SiegeAgent
            {
                targetEntity = Entity.Null, // Would be set to the siege target entity
                hasAssignedSlot = false,
                isQueuing = false,
                assignedSlotIndex = -1
            });
            
            // Transforms
            EntityManager.AddComponentData(entity, new SimulationTransformV2());
            EntityManager.AddComponentData(entity, new PreviousSimulationTransformV2());
            EntityManager.AddComponentData(entity, LocalTransform.Identity);
        }
    }
}
