# MovementV2 System

The MovementV2 system is a comprehensive, deterministic movement and pathfinding solution for the Avalon TD game. It provides advanced features including A* pathfinding, siege slot mechanics, local avoidance, and extensible AI movement capabilities.

## Key Features

### ✅ Deterministic Simulation
- Uses `dfloat`, `dfloat2`, `dfloat3` types for consistent cross-platform behavior
- Fixed timestep processing ensures reproducible results
- Burst-compiled jobs for optimal performance

### ✅ 2D Grid System with Link Traversal
- Efficient grid-based pathfinding and spatial queries
- Grid cell linking for complex terrain navigation
- Support for different movement types and walkability masks

### ✅ A* Pathfinding Algorithm
- Individual unit pathfinding with deterministic results
- Movement cost calculation and heuristic-based optimization
- Obstacle avoidance and dynamic path recalculation

### ✅ Path Walking with Local Avoidance
- Smooth path following with waypoint-based navigation
- Optional local avoidance to prevent unit clustering
- Collision prevention while maintaining path integrity

### ✅ Target Swarming (Siege Slot System)
- N enemies can target specific positions around objectives
- Rank-based trampling system (higher rank units displace lower rank)
- Automatic queuing when all siege slots are occupied
- Dynamic slot assignment and reservation system

### ✅ Extensible AI Movement Capabilities
- Component-based system for special movement abilities
- Support for teleporting, invisibility, flying, and phasing
- Cooldown and charge-based ability management
- Support unit capabilities for ally buffs

### ✅ Visualization Separation
- Clean separation between simulation and rendering
- Interpolation system for smooth visual movement
- Compatible with existing Unity Transform systems

## System Architecture

```
MovementV2SystemGroup
├── GridV2System                    (Grid management and updates)
├── SiegeSlotSystem                 (Siege slot assignment and trampling)
├── MovementAISystem                (Special AI movement abilities)
├── PathfindingSystem               (A* pathfinding calculations)
├── PathWalkingSystem               (Path following and local avoidance)
├── GridPositionUpdateSystem        (Grid position and occupancy updates)
└── SimulationTransformV2System     (Simulation transform updates)

Visualization
└── InterpolationV2System           (Smooth rendering interpolation)
```

## Core Components

### MovementV2Agent
Primary component containing movement properties, state, and capabilities.

### PathWalker
Handles path traversal, waypoint navigation, and local avoidance.

### GridPosition
Tracks entity position on the grid for spatial queries and pathfinding.

### SiegeTarget & SiegeAgent
Components for siege slot mechanics and target swarming behavior.

### MovementCapabilities
Extensible system for special AI movement abilities.

### SimulationTransformV2
Deterministic transform data separated from visualization.

## Movement Types

- **Ground**: Standard ground-based movement
- **Flying**: Can move over obstacles and unwalkable terrain
- **Amphibious**: Can traverse both land and water
- **Heavy**: Slower movement with limited path options
- **Teleporting**: Can teleport short distances
- **Invisible**: Can use stealth paths for flanking
- **Support**: Special movement for support units

## Usage Example

```csharp
// Create a MovementV2 agent entity
var entity = entityManager.CreateEntity();

// Add core movement components
entityManager.AddComponentData(entity, new MovementV2Agent
{
    maxSpeed = new dfloat(5.0d),
    acceleration = new dfloat(10.0d),
    movementType = MovementV2Type.Ground,
    unitRank = 1,
    useLocalAvoidance = true,
    avoidanceRadius = new dfloat(2.0d)
});

entityManager.AddComponentData(entity, new PathWalker
{
    waypointReachThreshold = new dfloat(0.5d),
    pathFollowingStrength = new dfloat(1.0d)
});

entityManager.AddComponentData(entity, new GridPosition());
entityManager.AddBuffer<PathWaypoint>(entity);

// Add special abilities (optional)
entityManager.AddComponentData(entity, new MovementCapabilities
{
    canTeleport = true,
    teleportRange = new dfloat(10.0d),
    teleportCharges = 2,
    maxTeleportCharges = 2
});

// Add simulation transform for visualization
entityManager.AddComponentData(entity, new SimulationTransformV2());
entityManager.AddComponentData(entity, new PreviousSimulationTransformV2());
```

## Integration with Existing Systems

The MovementV2 system integrates seamlessly with the existing FlowFieldSystemGroup:

```
FlowFieldSystemGroup
├── ... (existing systems)
├── MovementV2SystemGroup           (New MovementV2 systems)
└── MovementSystemGroup             (Original movement systems)
```

## Performance Considerations

- All systems use Burst compilation for optimal performance
- Grid-based spatial partitioning reduces computational complexity
- Efficient memory management with NativeContainers
- Deterministic math ensures consistent performance across platforms

## Testing

The system includes comprehensive unit tests covering:
- Grid utility functions and coordinate conversion
- Pathfinding algorithm accuracy
- Siege slot assignment and trampling mechanics
- Deterministic calculation consistency
- Component state management

Run tests using Unity Test Runner or NUnit framework.

## Future Enhancements

- Flow field pathfinding for large group movement
- Dynamic obstacle integration
- Advanced AI behaviors (formations, coordinated attacks)
- Performance profiling and optimization tools
- Visual debugging tools for pathfinding and grid state
