using Unity.Burst;
using Unity.Collections;
using Unity.Deterministic.Mathematics;
using Unity.Entities;
using Unity.Mathematics;
using Avalon.Simulation;
using Avalon.Simulation.MovementV2.Grid;
using Avalon.Simulation.MovementV2.Systems;

namespace Avalon.Simulation.MovementV2.AI
{
    /// <summary>
    /// System that processes special movement abilities for different AI types.
    /// Handles teleporting, invisibility, flanking, and support unit behaviors.
    /// </summary>
    [BurstCompile]
    [UpdateInGroup(typeof(MovementV2SystemGroup))]
    [UpdateAfter(typeof(SiegeSlotSystem))]
    [UpdateBefore(typeof(PathfindingSystem))]
    public partial struct MovementAISystem : ISystem
    {
        private EntityQuery capabilityQuery;
        private EntityQuery fixedTimestepQuery;
        
        [BurstCompile]
        public void OnCreate(ref SystemState state)
        {
            capabilityQuery = state.GetEntityQuery(new EntityQueryDesc
            {
                All = new ComponentType[]
                {
                    typeof(MovementCapabilities),
                    typeof(MovementV2Agent),
                    typeof(GridPosition)
                }
            });
            
            fixedTimestepQuery = state.GetEntityQuery(typeof(FixedTimestep));
            
            state.RequireForUpdate<FixedTimestep>();
        }
        
        [BurstCompile]
        public void OnUpdate(ref SystemState state)
        {
            // Get fixed timestep singleton
            if (fixedTimestepQuery.IsEmpty) return;
            var fixedTimestep = fixedTimestepQuery.GetSingleton<FixedTimestep>();
            
            // Only process on fixed ticks
            if (!fixedTimestep.shouldProcessTick) return;
            
            // Process movement abilities
            var abilityJob = new ProcessMovementAbilitiesJob
            {
                currentTime = fixedTimestep.currentTime,
                deltaTime = fixedTimestep.tickDuration,
                gridData = SystemAPI.GetSingleton<GridV2>(),
                gridCells = SystemAPI.GetSingletonBuffer<GridCellBuffer>().Reinterpret<GridCell>().AsNativeArray(),
                allAgentsLookup = SystemAPI.GetComponentLookup<MovementV2Agent>(true),
                gridPositionLookup = SystemAPI.GetComponentLookup<GridPosition>(true)
            };
            
            state.Dependency = abilityJob.ScheduleParallel(capabilityQuery, state.Dependency);
        }
    }
    
    /// <summary>
    /// Job that processes special movement abilities for AI units.
    /// </summary>
    [BurstCompile]
    public partial struct ProcessMovementAbilitiesJob : IJobEntity
    {
        public dfloat currentTime;
        public dfloat deltaTime;
        
        [ReadOnly] public GridV2 gridData;
        [ReadOnly] public NativeArray<GridCell> gridCells;
        [ReadOnly] public ComponentLookup<MovementV2Agent> allAgentsLookup;
        [ReadOnly] public ComponentLookup<GridPosition> gridPositionLookup;
        
        public void Execute(
            ref MovementCapabilities capabilities,
            ref MovementV2Agent agent,
            ref GridPosition gridPosition,
            ref ActiveMovementAbility activeAbility)
        {
            // Process active abilities first
            if (activeAbility.abilityType != MovementAbilityType.None)
            {
                ProcessActiveAbility(ref capabilities, ref agent, ref gridPosition, ref activeAbility);
                return;
            }
            
            // Check for ability triggers based on AI behavior
            CheckAbilityTriggers(ref capabilities, ref agent, ref gridPosition, ref activeAbility);
            
            // Update cooldowns
            UpdateCooldowns(ref capabilities, deltaTime);
            
            // Process support abilities
            if (capabilities.canBoostAllies)
            {
                ProcessSupportAbilities(capabilities, gridPosition);
            }
        }
        
        [BurstCompile]
        private void ProcessActiveAbility(
            ref MovementCapabilities capabilities,
            ref MovementV2Agent agent,
            ref GridPosition gridPosition,
            ref ActiveMovementAbility activeAbility)
        {
            dfloat elapsedTime = currentTime - activeAbility.startTime;
            
            switch (activeAbility.abilityType)
            {
                case MovementAbilityType.Teleport:
                    ProcessTeleport(ref agent, ref gridPosition, ref activeAbility, elapsedTime);
                    break;
                    
                case MovementAbilityType.Invisibility:
                    ProcessInvisibility(ref capabilities, ref agent, ref activeAbility, elapsedTime);
                    break;
                    
                case MovementAbilityType.Flight:
                    ProcessFlight(ref capabilities, ref agent, ref activeAbility, elapsedTime);
                    break;
                    
                case MovementAbilityType.Phase:
                    ProcessPhasing(ref agent, ref activeAbility, elapsedTime);
                    break;
                    
                case MovementAbilityType.SpeedBoost:
                    ProcessSpeedBoost(ref capabilities, ref agent, ref activeAbility, elapsedTime);
                    break;
            }
            
            // Check if ability duration has expired
            if (elapsedTime >= activeAbility.duration)
            {
                EndAbility(ref capabilities, ref agent, ref activeAbility);
            }
        }
        
        [BurstCompile]
        private void CheckAbilityTriggers(
            ref MovementCapabilities capabilities,
            ref MovementV2Agent agent,
            ref GridPosition gridPosition,
            ref ActiveMovementAbility activeAbility)
        {
            // Teleport trigger: when path is blocked or for flanking
            if (capabilities.canTeleport && 
                capabilities.teleportCharges > 0 &&
                (currentTime - capabilities.lastTeleportTime) >= capabilities.teleportCooldown)
            {
                if (ShouldTeleport(agent, gridPosition, capabilities))
                {
                    StartTeleport(ref capabilities, ref activeAbility, gridPosition);
                }
            }
            
            // Invisibility trigger: for flanking maneuvers
            if (capabilities.canGoInvisible &&
                (currentTime - capabilities.lastInvisibilityTime) >= capabilities.invisibilityCooldown)
            {
                if (ShouldGoInvisible(agent, gridPosition))
                {
                    StartInvisibility(ref capabilities, ref activeAbility);
                }
            }
        }
        
        [BurstCompile]
        private bool ShouldTeleport(MovementV2Agent agent, GridPosition gridPosition, MovementCapabilities capabilities)
        {
            // Teleport if path is blocked or for tactical positioning
            if (!agent.hasPath || !agent.isMoving)
                return false;
            
            // Check if there are obstacles ahead
            var currentGrid = gridPosition.gridCoordinates;
            var direction = dmath.normalize(agent.velocity);
            
            // Look ahead a few cells
            for (int i = 1; i <= 3; i++)
            {
                var checkPos = currentGrid + new int2(
                    (int)(direction.x * i),
                    (int)(direction.y * i)
                );
                
                if (GridV2Utils.IsValidGridPosition(checkPos, gridData.gridSize))
                {
                    var cellIndex = GridV2Utils.GridToIndex(checkPos, gridData.gridSize);
                    if (!gridCells[cellIndex].IsWalkableFor(agent.movementType))
                    {
                        return true; // Path is blocked, consider teleporting
                    }
                }
            }
            
            return false;
        }
        
        [BurstCompile]
        private bool ShouldGoInvisible(MovementV2Agent agent, GridPosition gridPosition)
        {
            // Go invisible for flanking or when surrounded
            // This is a simplified check - in practice, you'd check for enemy proximity
            return agent.isMoving && agent.hasPath;
        }
        
        [BurstCompile]
        private void StartTeleport(
            ref MovementCapabilities capabilities,
            ref ActiveMovementAbility activeAbility,
            GridPosition gridPosition)
        {
            // Calculate teleport target position
            var teleportDirection = dmath.normalize(new dfloat2(dfloat.One, dfloat.Zero)); // Simplified - should be tactical
            var teleportTarget = gridPosition.worldPosition + teleportDirection * capabilities.teleportRange;
            
            activeAbility.abilityType = MovementAbilityType.Teleport;
            activeAbility.startTime = currentTime;
            activeAbility.duration = new dfloat(0.1d); // Quick teleport
            activeAbility.targetPosition = teleportTarget;
            activeAbility.isChanneling = true;
            
            capabilities.teleportCharges--;
            capabilities.lastTeleportTime = currentTime;
        }
        
        [BurstCompile]
        private void StartInvisibility(
            ref MovementCapabilities capabilities,
            ref ActiveMovementAbility activeAbility)
        {
            activeAbility.abilityType = MovementAbilityType.Invisibility;
            activeAbility.startTime = currentTime;
            activeAbility.duration = capabilities.invisibilityDuration;
            activeAbility.isChanneling = false;
            
            capabilities.lastInvisibilityTime = currentTime;
        }
        
        [BurstCompile]
        private void ProcessTeleport(
            ref MovementV2Agent agent,
            ref GridPosition gridPosition,
            ref ActiveMovementAbility activeAbility,
            dfloat elapsedTime)
        {
            // Instant teleport at the end of channeling
            if (elapsedTime >= activeAbility.duration)
            {
                // Validate teleport target
                var targetGrid = GridV2Utils.WorldToGrid(activeAbility.targetPosition, gridData.worldOrigin, gridData.cellSize);
                
                if (GridV2Utils.IsValidGridPosition(targetGrid, gridData.gridSize))
                {
                    var cellIndex = GridV2Utils.GridToIndex(targetGrid, gridData.gridSize);
                    if (gridCells[cellIndex].IsWalkableFor(agent.movementType))
                    {
                        // Perform teleport
                        gridPosition.previousWorldPosition = gridPosition.worldPosition;
                        gridPosition.worldPosition = activeAbility.targetPosition;
                        gridPosition.gridCoordinates = targetGrid;
                        gridPosition.cellIndex = cellIndex;
                        gridPosition.hasMovedThisFrame = true;
                    }
                }
            }
        }
        
        [BurstCompile]
        private void ProcessInvisibility(
            ref MovementCapabilities capabilities,
            ref MovementV2Agent agent,
            ref ActiveMovementAbility activeAbility,
            dfloat elapsedTime)
        {
            // While invisible, modify movement behavior
            agent.maxSpeed *= new dfloat(1.2d); // Slight speed boost while invisible
            agent.useLocalAvoidance = false; // Don't avoid other units while invisible
        }
        
        [BurstCompile]
        private void ProcessFlight(
            ref MovementCapabilities capabilities,
            ref MovementV2Agent agent,
            ref ActiveMovementAbility activeAbility,
            dfloat elapsedTime)
        {
            // Flying units can move over obstacles
            agent.maxSpeed *= capabilities.flyingSpeedMultiplier;
        }
        
        [BurstCompile]
        private void ProcessPhasing(
            ref MovementV2Agent agent,
            ref ActiveMovementAbility activeAbility,
            dfloat elapsedTime)
        {
            // Phasing units can move through walls
            // This would require special pathfinding logic
        }
        
        [BurstCompile]
        private void ProcessSpeedBoost(
            ref MovementCapabilities capabilities,
            ref MovementV2Agent agent,
            ref ActiveMovementAbility activeAbility,
            dfloat elapsedTime)
        {
            agent.maxSpeed *= capabilities.speedBoostMultiplier;
        }
        
        [BurstCompile]
        private void ProcessSupportAbilities(MovementCapabilities capabilities, GridPosition gridPosition)
        {
            // Support units boost nearby allies
            // This would require querying nearby units and applying buffs
        }
        
        [BurstCompile]
        private void UpdateCooldowns(ref MovementCapabilities capabilities, dfloat deltaTime)
        {
            // Regenerate teleport charges over time
            if (capabilities.teleportCharges < capabilities.maxTeleportCharges)
            {
                // Simplified charge regeneration
                if ((currentTime - capabilities.lastTeleportTime) >= capabilities.teleportCooldown * new dfloat(2.0d))
                {
                    capabilities.teleportCharges++;
                }
            }
        }
        
        [BurstCompile]
        private void EndAbility(
            ref MovementCapabilities capabilities,
            ref MovementV2Agent agent,
            ref ActiveMovementAbility activeAbility)
        {
            // Reset any temporary modifications
            switch (activeAbility.abilityType)
            {
                case MovementAbilityType.Invisibility:
                    agent.useLocalAvoidance = true; // Restore avoidance
                    break;
            }
            
            // Clear active ability
            activeAbility.abilityType = MovementAbilityType.None;
            activeAbility.isChanneling = false;
        }
    }
}
