using Unity.Burst;
using Unity.Collections;
using Unity.Deterministic.Mathematics;
using Unity.Mathematics;
using Avalon.Simulation.MovementV2.Grid;

namespace Avalon.Simulation.MovementV2.Pathfinding
{
    /// <summary>
    /// A* pathfinding implementation for MovementV2 system.
    /// Uses deterministic math for consistent results across platforms.
    /// </summary>
    [BurstCompile]
    public struct AStarPathfinder
    {
        // Pathfinding data structures
        private NativeArray<AStarNode> nodes;
        private NativeList<int> openSet;
        private NativeHashSet<int> closedSet;
        private NativeList<int2> neighbors;
        
        // Grid data
        private int2 gridSize;
        private dfloat2 cellSize;
        private dfloat2 worldOrigin;
        
        public AStarPathfinder(int2 gridSize, dfloat2 cellSize, dfloat2 worldOrigin, Allocator allocator)
        {
            this.gridSize = gridSize;
            this.cellSize = cellSize;
            this.worldOrigin = worldOrigin;
            
            int totalCells = gridSize.x * gridSize.y;
            nodes = new NativeArray<AStarNode>(totalCells, allocator);
            openSet = new NativeList<int>(totalCells, allocator);
            closedSet = new NativeHashSet<int>(totalCells, allocator);
            neighbors = new NativeList<int2>(8, allocator);
        }
        
        /// <summary>
        /// Finds a path from start to goal using A* algorithm.
        /// </summary>
        [BurstCompile]
        public bool FindPath(
            int2 startGrid, 
            int2 goalGrid, 
            MovementV2Type movementType,
            NativeArray<GridCell> gridCells,
            ref NativeList<int2> pathResult)
        {
            pathResult.Clear();
            
            if (!GridV2Utils.IsValidGridPosition(startGrid, gridSize) ||
                !GridV2Utils.IsValidGridPosition(goalGrid, gridSize))
            {
                return false;
            }
            
            // Initialize nodes
            InitializeNodes();
            
            int startIndex = GridV2Utils.GridToIndex(startGrid, gridSize);
            int goalIndex = GridV2Utils.GridToIndex(goalGrid, gridSize);
            
            // Set up start node
            var startNode = nodes[startIndex];
            startNode.gCost = dfloat.Zero;
            startNode.hCost = CalculateHeuristic(startGrid, goalGrid);
            startNode.fCost = startNode.gCost + startNode.hCost;
            startNode.parentIndex = -1;
            nodes[startIndex] = startNode;
            
            openSet.Add(startIndex);
            
            while (openSet.Length > 0)
            {
                // Find node with lowest f cost
                int currentIndex = GetLowestFCostNode();
                openSet.RemoveAtSwapBack(GetOpenSetIndex(currentIndex));
                closedSet.Add(currentIndex);
                
                // Check if we reached the goal
                if (currentIndex == goalIndex)
                {
                    return ReconstructPath(startIndex, goalIndex, ref pathResult);
                }
                
                // Process neighbors
                int2 currentGrid = GridV2Utils.IndexToGrid(currentIndex, gridSize);
                GridV2Utils.GetNeighbors(currentGrid, gridSize, ref neighbors);
                
                for (int i = 0; i < neighbors.Length; i++)
                {
                    int2 neighborGrid = neighbors[i];
                    int neighborIndex = GridV2Utils.GridToIndex(neighborGrid, gridSize);
                    
                    if (closedSet.Contains(neighborIndex))
                        continue;
                    
                    // Check if neighbor is walkable
                    if (!gridCells[neighborIndex].IsWalkableFor(movementType))
                        continue;
                    
                    dfloat tentativeGCost = nodes[currentIndex].gCost + 
                        CalculateMovementCost(currentGrid, neighborGrid, gridCells[neighborIndex]);
                    
                    bool inOpenSet = openSet.Contains(neighborIndex);
                    
                    if (!inOpenSet || tentativeGCost < nodes[neighborIndex].gCost)
                    {
                        var neighborNode = nodes[neighborIndex];
                        neighborNode.gCost = tentativeGCost;
                        neighborNode.hCost = CalculateHeuristic(neighborGrid, goalGrid);
                        neighborNode.fCost = neighborNode.gCost + neighborNode.hCost;
                        neighborNode.parentIndex = currentIndex;
                        nodes[neighborIndex] = neighborNode;
                        
                        if (!inOpenSet)
                        {
                            openSet.Add(neighborIndex);
                        }
                    }
                }
            }
            
            return false; // No path found
        }
        
        [BurstCompile]
        private void InitializeNodes()
        {
            openSet.Clear();
            closedSet.Clear();
            
            for (int i = 0; i < nodes.Length; i++)
            {
                var node = new AStarNode
                {
                    gCost = dfloat.MAX_VALUE,
                    hCost = dfloat.Zero,
                    fCost = dfloat.MAX_VALUE,
                    parentIndex = -1
                };
                nodes[i] = node;
            }
        }
        
        [BurstCompile]
        private dfloat CalculateHeuristic(int2 from, int2 to)
        {
            // Using Euclidean distance as heuristic
            return GridV2Utils.EuclideanDistance(from, to);
        }
        
        [BurstCompile]
        private dfloat CalculateMovementCost(int2 from, int2 to, GridCell cell)
        {
            dfloat baseCost = GridV2Utils.EuclideanDistance(from, to);
            return baseCost * cell.movementCost;
        }
        
        [BurstCompile]
        private int GetLowestFCostNode()
        {
            int lowestIndex = openSet[0];
            dfloat lowestFCost = nodes[lowestIndex].fCost;
            
            for (int i = 1; i < openSet.Length; i++)
            {
                int nodeIndex = openSet[i];
                dfloat fCost = nodes[nodeIndex].fCost;
                
                if (fCost < lowestFCost)
                {
                    lowestFCost = fCost;
                    lowestIndex = nodeIndex;
                }
            }
            
            return lowestIndex;
        }
        
        [BurstCompile]
        private int GetOpenSetIndex(int nodeIndex)
        {
            for (int i = 0; i < openSet.Length; i++)
            {
                if (openSet[i] == nodeIndex)
                    return i;
            }
            return -1;
        }
        
        [BurstCompile]
        private bool ReconstructPath(int startIndex, int goalIndex, ref NativeList<int2> pathResult)
        {
            var path = new NativeList<int>(nodes.Length, Allocator.Temp);
            
            int currentIndex = goalIndex;
            while (currentIndex != startIndex && currentIndex != -1)
            {
                path.Add(currentIndex);
                currentIndex = nodes[currentIndex].parentIndex;
            }
            path.Add(startIndex);
            
            // Reverse path and convert to grid coordinates
            for (int i = path.Length - 1; i >= 0; i--)
            {
                pathResult.Add(GridV2Utils.IndexToGrid(path[i], gridSize));
            }
            
            path.Dispose();
            return pathResult.Length > 0;
        }
        
        public void Dispose()
        {
            if (nodes.IsCreated) nodes.Dispose();
            if (openSet.IsCreated) openSet.Dispose();
            if (closedSet.IsCreated) closedSet.Dispose();
            if (neighbors.IsCreated) neighbors.Dispose();
        }
    }
    
    /// <summary>
    /// Node data structure for A* pathfinding.
    /// </summary>
    public struct AStarNode
    {
        public dfloat gCost; // Distance from start
        public dfloat hCost; // Heuristic distance to goal
        public dfloat fCost; // Total cost (g + h)
        public int parentIndex; // Parent node index for path reconstruction
    }
}
